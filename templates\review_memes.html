<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Review Memes</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/memes.css">
</head>
<body>
    {% include 'navbar.html' %}
    <div class="container">
        <h1>📝 Text Review Phase</h1>
        <p>Add optional text for narration and approve memes to proceed to the next stage.</p>

        {% if memes_by_subreddit %}
            {% for subreddit, memes in memes_by_subreddit.items() %}
            <div class="subreddit-section">
                <h2>📂 r/{{ subreddit }} ({{ memes|length }} memes)</h2>

                {% for meme in memes %}
                <div class="meme-editor">
                    <div class="meme-image">
                        <img src="{{ meme.url }}" alt="Meme Image"
                             onclick="openImageModal(this.src)"
                             style="max-width: 400px; max-height: 300px; object-fit: contain; cursor: pointer;">
                    </div>
                    <div class="meme-text-editor">
                        <div class="meme-status">
                            {% if meme.text_approved %}
                                <span class="status-approved">✅ Approved for next stage</span>
                            {% else %}
                                <span class="status-pending">⏳ Pending approval</span>
                            {% endif %}
                        </div>

                        <div class="workflow-actions">
                            {% if not meme.text_approved %}
                            <div class="meme-text-editor-section" style="margin-bottom: 1rem;">
                                <label for="text_{{ meme.id }}">Meme Text (Optional):</label>
                                <textarea id="text_{{ meme.id }}" name="text" rows="4" cols="50"
                                          placeholder="Add text for narration (leave blank for image-only video)"
                                          onchange="saveMemeText({{ meme.id }}, this.value)">{{ meme.text or '' }}</textarea>
                            </div>
                            <button type="button" class="btn btn-success" onclick="approveMeme({{ meme.id }})">✅ Approve & Continue</button>
                            {% endif %}

                            <button type="button" class="btn btn-danger" onclick="discardMeme({{ meme.id }})">🗑️ Discard</button>
                        </div>
                    </div>
                </div>
                <hr>
                {% endfor %}
            </div>
            {% endfor %}

            <div class="actions">
                <a href="/audio_workshop" class="btn btn-secondary">🎵 Continue to Audio Workshop</a>
                <a href="/dashboard" class="btn btn-primary">🏠 Back to Dashboard</a>
            </div>
        {% else %}
            <p>No memes available for editing. <a href="/meme_workshop" class="btn btn-primary">🎭 Go to Meme Workshop</a> to fetch some memes first!</p>
        {% endif %}
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
        <img class="image-modal-content" id="modalImage">
    </div>

    <script>
        function openImageModal(src) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            modal.classList.add('show');
            modalImg.src = src;
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('show');
        }

        // Close modal when clicking outside the image
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });

        // Save meme text without page reload
        function saveMemeText(memeId, text) {
            fetch('/update_meme_text', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `meme_id=${memeId}&text=${encodeURIComponent(text)}`
            });
        }

        // Helper function to update subreddit meme count
        function updateSubredditCount(subredditSection) {
            const memeCount = subredditSection.querySelectorAll('.meme-editor').length;
            const header = subredditSection.querySelector('h2');
            if (header) {
                const subredditName = header.textContent.match(/r\/(\w+)/)[1];
                header.textContent = `📂 r/${subredditName} (${memeCount} memes)`;
            }
        }

        // Helper function to remove meme from page with animation
        function removeMemeFromPage(memeEditor) {
            memeEditor.style.transition = 'opacity 0.3s ease';
            memeEditor.style.opacity = '0';
            setTimeout(() => {
                const subredditSection = memeEditor.closest('.subreddit-section');
                memeEditor.remove();

                // Update meme count in subreddit header
                if (subredditSection) {
                    updateSubredditCount(subredditSection);

                    // Check if subreddit section is empty and remove it
                    if (subredditSection.querySelectorAll('.meme-editor').length === 0) {
                        subredditSection.remove();
                    }
                }
            }, 300);
        }

        // Approve meme and remove from page
        function approveMeme(memeId) {
            const textarea = document.getElementById(`text_${memeId}`);
            const text = textarea.value;

            // Save text first, then approve
            fetch('/approve_meme_text', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `meme_id=${memeId}&text=${encodeURIComponent(text)}`
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the meme from the page
                    const memeEditor = textarea.closest('.meme-editor');
                    removeMemeFromPage(memeEditor);
                } else {
                    alert('Error approving meme: ' + data.message);
                }
            }).catch(error => {
                console.error('Error:', error);
                alert('Error approving meme. Please try again.');
            });
        }

        // Discard meme and remove from page
        function discardMeme(memeId) {
            if (!confirm('Are you sure you want to discard this meme?')) {
                return;
            }

            fetch('/discard_meme', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `meme_id=${memeId}`
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Find the meme editor element and remove it
                    const memeEditor = document.querySelector(`textarea[id="text_${memeId}"]`).closest('.meme-editor');
                    removeMemeFromPage(memeEditor);
                } else {
                    alert('Error discarding meme: ' + data.message);
                }
            }).catch(error => {
                console.error('Error:', error);
                alert('Error discarding meme. Please try again.');
            });
        }
    </script>
</body>
</html>
