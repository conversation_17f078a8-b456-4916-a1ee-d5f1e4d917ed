/* Enhanced Dashboard Styles */

/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, var(--bg-card) 0%, var(--bg-secondary) 100%);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    margin-bottom: 2rem;
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(139, 92, 246, 0.1) 50%, transparent 70%);
    animation: headerShine 3s ease-in-out infinite;
}

@keyframes headerShine {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

.dashboard-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    position: relative;
    z-index: 1;
}

/* Navigation Grid */
.navigation {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
}

.navigation a {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    color: white;
    padding: 1rem 1.5rem;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
}

.navigation a:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Actions Section */
.actions {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--gray-100);
    border-radius: var(--border-radius-lg);
    text-align: center;
    border: 1px solid var(--gray-200);
}

/* Dashboard Stats */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--bg-card);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-primary);
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(139, 92, 246, 0.05) 50%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
    border-color: var(--border-accent);
}

.stat-card:hover::after {
    opacity: 1;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    text-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
}

.stat-label {
    color: var(--text-primary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
}

.stat-description {
    color: var(--text-secondary);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Stat Card Variants */
.stat-card.success .stat-number {
    color: var(--success-color);
}

.stat-card.warning .stat-number {
    color: var(--warning-color);
}

.stat-card.info .stat-number {
    color: var(--secondary-color);
}

.stat-card.need-attention .stat-number {
    color: var(--danger-color);
}

/* Workflow Progress */
.workflow-progress {
    background: var(--bg-card);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    margin: 1.5rem 0;
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
}

.workflow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.workflow-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
}

.workflow-percentage {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.875rem;
}

.workflow-details {
    margin-top: 0.75rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-align: center;
}

/* Progress Bars */
.progress-container {
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid var(--border-primary);
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, var(--success-color) 0%, var(--secondary-color) 100%);
    border-radius: 6px;
    transition: width 0.5s ease;
    box-shadow: 0 0 15px rgba(52, 211, 153, 0.4);
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
    text-align: center;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.quick-action {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
    color: var(--gray-800);
}

.quick-action:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    text-decoration: none;
    color: var(--primary-color);
}

.quick-action-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.quick-action-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.quick-action-description {
    font-size: 0.875rem;
    color: var(--gray-600);
}

/* Recent Activity */
.recent-activity {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    margin-top: 2rem;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-100);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--gray-500);
}

/* Subreddit Progress Styles */
.subreddit-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.subreddit-card {
    background: var(--bg-card);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.subreddit-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
    border-color: var(--border-accent);
}

.subreddit-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    text-align: center;
    font-size: 1.1rem;
    font-weight: 600;
}

.subreddit-progress {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 60px;
}

.step-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.step-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

.progress-arrow {
    color: var(--text-muted);
    font-size: 1.2rem;
    margin: 0 0.5rem;
}

.subreddit-percentage {
    text-align: center;
    font-weight: 600;
    color: var(--success-color);
    font-size: 0.875rem;
    padding: 0.5rem;
    background: rgba(52, 211, 153, 0.1);
    border-radius: var(--border-radius);
    border: 1px solid rgba(52, 211, 153, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .navigation {
        grid-template-columns: 1fr;
    }

    nav .nav-container {
        flex-direction: column;
        gap: 0.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }

    .activity-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .subreddit-stats {
        grid-template-columns: 1fr;
    }

    .subreddit-progress {
        flex-direction: column;
        gap: 1rem;
    }

    .progress-arrow {
        transform: rotate(90deg);
        margin: 0.5rem 0;
    }

    .admin-actions {
        padding: 1rem;
    }

    .admin-actions .btn-danger {
        width: 100%;
        padding: 1rem;
    }
}

/* Admin Actions */
.admin-actions {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--bg-card);
    border: 2px solid var(--danger-color);
    border-radius: var(--border-radius-lg);
    text-align: center;
}

.admin-actions .btn-danger {
    background: linear-gradient(135deg, var(--danger-color), var(--danger-dark));
    border-color: var(--danger-color);
    font-weight: 600;
    padding: 1rem 2rem;
    font-size: 1rem;
}

.admin-actions .btn-danger:hover {
    background: linear-gradient(135deg, var(--danger-dark), #dc2626);
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}
