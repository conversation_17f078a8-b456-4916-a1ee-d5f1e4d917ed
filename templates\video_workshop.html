<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Workshop</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/memes.css">
    <link rel="stylesheet" href="/static/css/video.css">
</head>
<body>
    {% include 'navbar.html' %}
    <div class="container">
        <!-- Flash Messages -->
        {% if session.flash_message and ('video_workshop' in session.flash_context or 'video' in session.flash_context) %}
        <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0;">
            {{ session.flash_message }}
            {% set _ = session.pop('flash_message') %}
            {% set _ = session.pop('flash_context') %}
        </div>
        {% endif %}

        <h1>🎬 Video Workshop</h1>
        <p>Generate compilation videos from your approved memes and audio.</p>

        <!-- Video Generation Settings -->
        <div class="video-settings">
            <h2>📋 Current Video Settings</h2>
            <div class="settings-summary">
                <div class="setting-item">
                    <span class="setting-label">Regular Videos (16:9):</span>
                    <span class="setting-value {% if config.create_videos %}enabled{% else %}disabled{% endif %}">
                        {% if config.create_videos %}✅ Enabled{% else %}❌ Disabled{% endif %}
                    </span>
                </div>
                <div class="setting-item">
                    <span class="setting-label">Shorts (9:16):</span>
                    <span class="setting-value {% if config.create_shorts %}enabled{% else %}disabled{% endif %}">
                        {% if config.create_shorts %}✅ Enabled{% else %}❌ Disabled{% endif %}
                    </span>
                </div>
                <a href="/config" class="btn btn-secondary">⚙️ Change Settings</a>
            </div>
        </div>

        <!-- Video Requirements Section -->
        <div class="video-requirements-section">
            <h2>📊 Video Generation Requirements</h2>

            <!-- Regular Video Progress -->
            {% if config.create_videos %}
            <div class="requirement-card">
                <h3>🎬 Regular Video (16:9)</h3>
                <div class="requirement-info">
                    <span>Minimum: 10 minutes | Available: {{ "%.1f"|format(requirements.regular.current_duration / 60) }} minutes</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ requirements.regular.progress_percent }}%"></div>
                </div>
                <div class="progress-text">{{ "%.0f"|format(requirements.regular.progress_percent) }}% complete</div>

                {% if requirements.regular.can_generate %}
                <button type="button" class="btn btn-primary" onclick="generateVideo('regular')">
                    🎬 Generate Regular Video ({{ requirements.regular.available_memes }} memes)
                </button>
                {% else %}
                <div class="requirement-status">
                    Need {{ "%.1f"|format((requirements.regular.min_duration - requirements.regular.current_duration) / 60) }} more minutes of content
                </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- Shorts Progress -->
            {% if config.create_shorts %}
            <div class="requirement-card">
                <h3>📱 Shorts Video (9:16)</h3>
                <div class="requirement-info">
                    <span>Minimum: 1 minute | Maximum: 3 minutes | Available: {{ "%.1f"|format(requirements.shorts.current_duration / 60) }} minutes</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ requirements.shorts.progress_percent }}%"></div>
                </div>
                <div class="progress-text">{{ "%.0f"|format(requirements.shorts.progress_percent) }}% complete</div>

                {% if requirements.shorts.can_generate %}
                <button type="button" class="btn btn-primary" onclick="generateVideo('shorts')">
                    📱 Generate Shorts Video ({{ requirements.shorts.available_memes }} memes)
                </button>
                {% else %}
                <div class="requirement-status">
                    Need {{ "%.1f"|format((requirements.shorts.min_duration - requirements.shorts.current_duration) / 60) }} more minutes of content
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <!-- Ready Memes Section -->
        {% if memes_by_subreddit %}
        <div class="ready-memes-section">
            <h2>🎯 Available Memes for Video Generation</h2>
            <p>These memes are ready to be used in videos based on your settings.</p>

            {% for subreddit, memes in memes_by_subreddit.items() %}
            <div class="subreddit-section">
                <h3>📂 r/{{ subreddit }} ({{ memes|length }} memes ready)</h3>
                
                <div class="memes-grid">
                    {% for meme in memes %}
                    <div class="meme-preview">
                        <div class="meme-image">
                            <img src="/{{ meme.image_path }}" alt="Meme" onclick="openImageModal(this.src)">
                        </div>
                        <div class="meme-info">
                            <div class="meme-text">
                                {% if meme.text %}
                                    <strong>Text:</strong> {{ meme.text[:100] }}{% if meme.text|length > 100 %}...{% endif %}
                                {% else %}
                                    <em>No text (image-only, 3s duration)</em>
                                {% endif %}
                            </div>
                            <div class="video-type-indicators">
                                {% if meme.used_in_regular_video %}
                                    <span class="type-indicator used">🎬 Used in Regular</span>
                                {% elif config.create_videos %}
                                    <span class="type-indicator available">🎬 Available for Regular</span>
                                {% endif %}
                                {% if meme.used_in_shorts_video %}
                                    <span class="type-indicator used">📱 Used in Shorts</span>
                                {% elif config.create_shorts %}
                                    <span class="type-indicator available">📱 Available for Shorts</span>
                                {% endif %}
                            </div>
                            {% set audio_path = session.get('audio_path_' + meme.id|string) %}
                            {% if audio_path %}
                            <div class="audio-preview">
                                <audio controls style="width: 100%; margin-top: 0.5rem;">
                                    <source src="/{{ audio_path }}" type="audio/wav">
                                </audio>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="no-content">
            <h2>🎯 No Memes Ready for Video Generation</h2>
            <p>You need memes that have been approved in the audio workshop first.</p>
            <a href="/audio_workshop" class="btn btn-primary">🎵 Go to Audio Workshop</a>
        </div>
        {% endif %}

        <!-- Generated Videos Section -->
        {% if generated_videos %}
        <div class="generated-videos-section">
            <h2>📹 Generated Videos</h2>
            <div class="videos-grid">
                {% for video in generated_videos %}
                <div class="video-card">
                    <div class="video-info">
                        <h4>
                            {% if video.video_type == 'shorts' %}
                                📱 Shorts Video
                            {% else %}
                                🎬 Regular Video
                            {% endif %}
                        </h4>
                        <div class="video-stats">
                            <span class="stat">⏱️ {{ "%.1f"|format(video.duration) }}s</span>
                            <span class="stat">🎭 {{ video.memes_count }} memes</span>
                            <span class="stat">📅 {{ video.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        </div>
                    </div>
                    <div class="video-actions">
                        <a href="/{{ video.video_path }}" class="btn btn-primary" target="_blank">▶️ Preview</a>
                        <a href="/{{ video.video_path }}" class="btn btn-secondary" download>⬇️ Download</a>
                        <button type="button" class="btn btn-danger" onclick="showDiscardModal({{ video.id }}, '{{ video.video_type }}', {{ video.memes_count }})">🗑️ Discard</button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <div class="actions">
            <a href="/audio_workshop" class="btn btn-secondary">⬅️ Back to Audio Workshop</a>
            <a href="/dashboard" class="btn btn-primary">🏠 Back to Dashboard</a>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
        <img class="image-modal-content" id="modalImage">
    </div>

    <!-- Discard Video Modal -->
    <div id="discardModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🗑️ Discard Video</h3>
                <span class="modal-close" onclick="closeDiscardModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p>What would you like to do with the <span id="memeCount"></span> memes used in this <span id="videoType"></span> video?</p>
                <div class="discard-options">
                    <button type="button" class="btn btn-primary" onclick="discardVideo('return_memes')">
                        ↩️ Return memes to approved list
                        <small>Make them available for new video generation</small>
                    </button>
                    <button type="button" class="btn btn-danger" onclick="discardVideo('discard_memes')">
                        🗑️ Discard memes permanently
                        <small>Remove memes from workflow entirely</small>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Image modal functionality
        function openImageModal(src) {
            document.getElementById('imageModal').style.display = 'block';
            document.getElementById('modalImage').src = src;
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target === modal) {
                closeImageModal();
            }
        }

        // Generate videos function
        function generateVideo(videoType) {
            const button = document.querySelector(`button[onclick="generateVideo('${videoType}')"]`);
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '⏳ Generating...';
            button.disabled = true;

            const formData = new FormData();
            formData.append('video_type', videoType);

            fetch('/generate_videos', {
                method: 'POST',
                body: formData
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message and reload page
                    if (data.message) {
                        alert('Success: ' + data.message);
                    }
                    location.reload();
                } else {
                    alert('Error: ' + (data.message || 'Unknown error'));
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }).catch(error => {
                console.error('Error:', error);
                alert('Error generating video. Please try again.');
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }

        // Discard modal functionality
        let currentVideoId = null;

        function showDiscardModal(videoId, videoType, memeCount) {
            currentVideoId = videoId;
            document.getElementById('videoType').textContent = videoType;
            document.getElementById('memeCount').textContent = memeCount;
            document.getElementById('discardModal').style.display = 'block';
        }

        function closeDiscardModal() {
            document.getElementById('discardModal').style.display = 'none';
            currentVideoId = null;
        }

        function discardVideo(action) {
            if (!currentVideoId) return;

            const formData = new FormData();
            formData.append('video_id', currentVideoId);
            formData.append('action', action);

            fetch('/discard_video', {
                method: 'POST',
                body: formData
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Success: ' + data.message);
                    closeDiscardModal();
                    location.reload();
                } else {
                    alert('Error: ' + (data.message || 'Unknown error'));
                }
            }).catch(error => {
                console.error('Error:', error);
                alert('Error discarding video. Please try again.');
            });
        }
    </script>
</body>
</html>
